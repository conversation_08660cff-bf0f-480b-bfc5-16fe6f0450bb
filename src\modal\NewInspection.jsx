import React, { useEffect, useState } from 'react';
import { CustomModal } from '../common/styledComponent';
import { useTranslation } from 'react-i18next';
import { Button, Checkbox, InputNumber, Select } from 'antd';
import { useAcquireConveyorControlMutation, useGetAllConveyorStatusQuery, useLazyGetAllConveyorStatusQuery } from '../services/conveyor';
import _, { set } from 'lodash';
import { useGetInferenceStatusQuery, useLazyGetAllSessionsQuery, useLazyGetInferenceStatusQuery, useStartContinuousInferenceMutation, useStartInferenceSessionMutation } from '../services/inference';
import { ALERT_TYPES, aoiAlert } from '../common/alert';
import { conveyorUsage, modelTypes, conveyorNum, errorMessage, serverHost, localStorageKeys, retrainModelTaskPhaseType } from '../common/const';
import { useNavigate } from 'react-router-dom';
import { setContainerLvlLoadingMsg, setConveyorAccessToken, setCurrentControlledConveyorSlotId, setIsContainerLvlLoadingEnabled } from '../reducer/setting';
import { useDispatch, useSelector } from 'react-redux';
import { getCurrentConveyorStatus } from '../common/util';
import { useShouldUpdateModelsMutation } from '../services/product';


const NewInspection = (props) => {
  const {
    isOpened,
    setIsOpened,
    allProducts,
  } = props;
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [selectedProductId, setSelectedProductId] = useState(null);
  const [selectedConveyorSlot, setSelectedConveyorSlot] = useState(null);
  const [conveyorAssignedWidth, setConveyorAssignedWidth] = useState(10);
  // const [isStopWhenDefectDetectedEnabled, setIsStopWhenDefectDetectedEnabled] = useState(false);
  const [isContinuousModeEnabled, setIsContinuousModeEnabled] = useState(true);
  const [options, setOptions] = useState([]);
  const [conveyorStatus, setConveyorStatus] = useState(null);
  const [conveyorSelection, setConveyorSelection] = useState([]);

  // const { data: conveyorStatus, refetch: refetchConveyorStatus, isLoading: conveyorLoading, isFetching: conveyorStatusFetching } = useGetAllConveyorStatusQuery();
  const { data: inferenceStatus, refetch: refetchInferenceStatus } = useGetInferenceStatusQuery();
  const [startInspection] = useStartInferenceSessionMutation();
  const [acquireConveyorControl] = useAcquireConveyorControlMutation();
  const [getAllSession] = useLazyGetAllSessionsQuery();
  const [lazyGetConveyorStatus] = useLazyGetAllConveyorStatusQuery();
  const [lazyGetInferenceStatus] = useLazyGetInferenceStatusQuery();
  const [enableContinuousInference] = useStartContinuousInferenceMutation();
  const [shouldUpdateModels] = useShouldUpdateModelsMutation();

  const handleStartInspection = async (productId, selectedConveyorSlot, isContEnabled) => {
    // need to check if this product's model has update available ow block user
    const res1 = await shouldUpdateModels({
      model_types: [
        modelTypes.mountingModel,
        modelTypes.leadModel
      ],
      golden_product_id: Number(productId),
    });

    if (_.some(res1.data, d => d)) {
      aoiAlert(t('notification.error.selectedPCBAProgramNeedsToBeRetrained'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    let modelStatusRes;
    
    try {
      modelStatusRes = await fetch(`${serverHost}/getModelUpdates`, {
        method: 'GET',
        headers: {
          'Authorization': localStorage.getItem(localStorageKeys.accessToken) || '',
        },
      });
    } catch (error) {
      console.error('Failed to fetch model status');
      return;
    }

    const modelStatus = await modelStatusRes.json();

    // check if any model training task with this product id is in progress
    if (!_.isEmpty(_.filter(modelStatus, (taskStatus) => taskStatus.golden_product_id === Number(productId) && !_.includes([
      retrainModelTaskPhaseType.failure,
      retrainModelTaskPhaseType.complete,
      retrainModelTaskPhaseType.invalid,
      retrainModelTaskPhaseType.partialFailure,
    ], taskStatus.phase)))) {
      aoiAlert(t('notification.error.pleaseWaitForTrainingToCompleteBefore'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.startingInspection')));

    const res = await startInspection({ product_id: productId, conveyor_id: Number(selectedConveyorSlot) });

    // console.log('startInspection res: ', res);

    if (_.get(res, 'error.data.message', '') === errorMessage.startInferenceWithoutMarker) {
      aoiAlert(t('notification.error.startInferenceWithoutMarker'), ALERT_TYPES.COMMON_ERROR);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    if (res.error) {
      aoiAlert(t('notification.error.startInspection'), ALERT_TYPES.COMMON_ERROR);
      console.error(res.error.message);
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    if (isContEnabled) {
      const enableRes = await enableContinuousInference({
        product_id: productId, conveyor_id: Number(selectedConveyorSlot)
      });

      if (enableRes.error) {
        aoiAlert(t('notification.error.enableContinuousInspection'), ALERT_TYPES.COMMON_ERROR);
        console.error(enableRes.error.message);
        dispatch(setIsContainerLvlLoadingEnabled(false));
        dispatch(setContainerLvlLoadingMsg(''));
        return;
      }
    }

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));

    navigate(`/inspection/live?running-session-id=${_.get(res, 'data.ipc_session_id', 0)}&slot-id=${selectedConveyorSlot}&golden-product-id=${productId}`);
  };

  useEffect(() => {
    const options = [];
    for (let i = 0; i < conveyorNum; i++) {
      options.push({
        value: `${i}`,
        label: (
          <div className='flex px-1 items-center gap-2'>
            <span className='font-source text-[14px] font-normal leading-[136%]'>
              {String.fromCharCode(65 + i)}
            </span>
            {_.get(conveyorStatus, `${i}.taskType`, 'none') === 'none' ? (
              <div className='flex items-center gap-1.5 p-1.5'>
                <div className='w-[10px] h-[10px] rounded-full bg-[#81F499]' />
                <span className='font-source text-[12px] font-normal leading-[normal] italic'>
                  {t('productDefine.available')}
                </span>
              </div>
            ) : (
              <div className='flex items-center gap-1.5 p-1.5'>
                <div className='w-[10px] h-[10px] rounded-full bg-[#EB5757]' />
                <span className='font-source text-[12px] font-normal leading-[normal] italic'>
                  {t('productDefine.inUse')}
                </span>
                -
                <span className='font-source text-[12px] font-normal leading-[normal] italic'>
                  {t(`home.${_.get(conveyorStatus, `${i}.taskType`, 'none')}`)}
                </span>
              </div>
            )}
          </div>
        ),
        disabled: _.get(conveyorStatus, `${i}.taskType`, 'none') !== 'none',
      });
    }
    setConveyorSelection(options);
  }, [conveyorStatus]);

  return (
    <CustomModal
      width={468}
      open={isOpened}
      onCancel={() => setIsOpened(false)}
      title={<span className='font-source text-[16px] font-semibold leading-[136%]'>
        {t('newInspectionTask.newInspectionTask')}
      </span>}
      footer={null}
    >
      <div className='flex flex-col self-stretch flex-1'>
        <div className='flex py-6 px-4 flex-col gap-8 self-stretch'>
          <div className='flex flex-col gap-2 self-stretch'>
            <div className='flex gap-6 items-center self-stretch'>
              <div className='flex self-stretch items-center w-[36%]'>
                <span className='font-source text-[12px] font-semibold leading-[136%] whitespace-nowrap'>
                  {t('newInspectionTask.pickAPCB')}
                </span>
              </div>
              <div className='flex flex-1 self-stretch'>
                <Select
                  popupMatchSelectWidth={false}
                  style={{ width: '100%' }}
                  value={selectedProductId}
                  options={allProducts?.map((product) => ({
                    value: Number(product.product_id),
                    label: <span className='font-source text-[12px] font-normal leading-[136%]'>
                      {product.product_name}
                    </span>,
                  }))}
                  onChange={(value) => setSelectedProductId(value)}
                />
              </div>
            </div>
            <div className='flex gap-6 items-center self-stretch'>
              <div className='flex self-stretch items-center w-[36%]'>
                <span className='font-source text-[12px] font-semibold leading-[136%]'>
                  {t('newInspectionTask.conveyor')}
                </span>
              </div>
              <div className='flex flex-1 self-stretch gap-2'>
                <Select
                  popupMatchSelectWidth={false}
                  style={{ width: '100%' }}
                  options={conveyorSelection}
                  value={selectedConveyorSlot}
                  onChange={(v) => setSelectedConveyorSlot(v)}
                />
                <Button
                  type='text'
                  onClick={() => {
                    const run = async () => {
                      let conveyorStatus;
                      try {
                        conveyorStatus = await getCurrentConveyorStatus(lazyGetConveyorStatus, lazyGetInferenceStatus, t);
                      } catch (e) {
                        console.error(e);
                        return;
                      }
                      
                      setConveyorStatus(conveyorStatus);
                    };

                    run();
                  }}
                >
                  <span className='font-source text-[12px] font-normal leading-[136%] text-AOI-blue'>
                    {t('newInspectionTask.refreshStatus')}
                  </span>
                </Button>
              </div>
            </div>
            {/* <div className='flex gap-6 items-center self-stretch'>
              <span className='font-source text-[12px] font-semibold leading-[136%]'>
                {t('newInspectionTask.adjustWidth')}
              </span>
              <div className='flex flex-1 self-stretch'>
                <InputNumber
                  disabled={!selectedConveyorSlot}
                  style={{ width: '100%' }}
                  value={conveyorAssignedWidth}
                  onChange={(value) => setConveyorAssignedWidth(value)}
                  controls={false}
                />
              </div>
            </div> */}
            <div className='flex gap-6 items-center self-stretch'>
              <div className='flex self-stretch items-center w-[36%]'>
                <span className='font-source text-[12px] font-semibold leading-[136%]'>
                  {t('newInspectionTask.selectTaskMode')}
                </span>
              </div>
              <div className='flex flex-1 self-stretch'>
                <Select
                  popupMatchSelectWidth={false}
                  style={{ width: '100%' }}
                  value={isContinuousModeEnabled ? 0 : 1}
                  options={[
                    {
                      value: 0,
                      label: <span className='font-source text-[12px] font-normal leading-[136%]'>
                        {t('newInspectionTask.runTaskContinuously')}
                      </span>,
                    },
                    {
                      value: 1,
                      label: <span className='font-source text-[12px] font-normal leading-[136%]'>
                        {t('newInspectionTask.runTaskByBtnTrigger')}
                      </span>,
                    }
                  ]}
                  onChange={(value) => setIsContinuousModeEnabled(value === 0)}
                />
              </div>
            </div>
          </div>
          {/* <div className='flex items-center gap-2'>
            <Checkbox
              checked={isStopWhenDefectDetectedEnabled}
              onChange={() => setIsStopWhenDefectDetectedEnabled(!isStopWhenDefectDetectedEnabled)}
            />
            <span className='font-source text-[12px] font-normal leading-[136%] pt-0.5'>
              {t('newInspectionTask.stopInspectionAuto')}
            </span>
          </div> */}
        </div>
        <div className='flex p-4 gap-2 self-stretch flex-1'>
          <Button
            style={{ width: '50%' }}
            onClick={() => setIsOpened(false)}
          >
            <span className='font-source text-[12px] font-normal leading-[136%]'>
              {t('common.cancel')}
            </span>
          </Button>
          <Button
            disabled={!selectedProductId || !selectedConveyorSlot}
            style={{ width: '50%' }}
            type='primary'
            onClick={() => handleStartInspection(selectedProductId, selectedConveyorSlot, isContinuousModeEnabled)}
          >
            <span className='font-source text-[12px] font-normal leading-[136%]'>
              {t('newInspectionTask.startInspection')}
            </span>
          </Button>
        </div>
      </div>
    </CustomModal>
  )
};

export default NewInspection;