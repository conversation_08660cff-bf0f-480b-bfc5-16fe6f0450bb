import { Button, Checkbox, InputNumber, Tooltip } from 'antd';
import React, { useEffect, useState, useRef, Fragment } from 'react';
import { useTranslation } from 'react-i18next';
import { ALERT_TYPES, aoiAlert } from '../../../../common/alert';
import _ from 'lodash';
import { useGoldenRegisterArrayMutation } from '../../../../services/product';
import { arrayBoardSelectionRoiStrokeWidth } from '../../../../common/const';
import { useDispatch } from 'react-redux';
import { setContainerLvlLoadingMsg, setIsContainerLvlLoadingEnabled } from '../../../../reducer/setting';


const SmartPCBArray = (props) => {
  const {
    arrayRegisteration,
    setActiveTab,
    curSelectedMarker,
    setCurSelectedMarker,
    setSelectedTool,
    curProduct,
    refetchArrayRegisteration,
    handleRefetchAllComponents,
    curMarkers,
    setCurMarkers,
    isSubBoardSelectionRoiDisplayed,
    setIsSubBoardSelectionRoiDisplayed,
    setPreviewArrayTransforms,
    setComponentsActiveTab,
  } = props;

  const dispatch = useDispatch();

  const { t } = useTranslation();

  const curSelectingMarker = useRef(null);
  const [gridRowCount, setGridRowCount] = useState(2);
  const [gridColumnCount, setGridColumnCount] = useState(2);

  const [goldenRegisterArray] = useGoldenRegisterArrayMutation();

  useEffect(() => {
    if (!setPreviewArrayTransforms) return;

    // if (!curMarkers.tl || !curMarkers.tr || !curMarkers.bl) {
    //   setPreviewArrayTransforms(null);
    //   return;
    // }

    if (
      !curMarkers.tl ||
      (gridRowCount > 1 && !curMarkers.tr) ||
      (gridColumnCount > 1 && !curMarkers.bl)
    ) {
      setPreviewArrayTransforms(null);
      return;
    }

    // const markerWidth = curMarkers.tr.x - curMarkers.tl.x;
    // const markerHeight = curMarkers.bl.y - curMarkers.tl.y;

    const markerWidth = gridRowCount > 1 ? curMarkers.tr.x - curMarkers.tl.x : 0;
    const markerHeight = gridColumnCount > 1 ? curMarkers.bl.y - curMarkers.tl.y : 0;

    // if (markerWidth <= 0 || markerHeight <= 0) {
    //   setPreviewArrayTransforms(null);
    //   return;
    // }

    if (
      (markerWidth <= 0 && gridRowCount > 1) ||
      (markerHeight <= 0 && gridColumnCount > 1)
    ) {
      setPreviewArrayTransforms(null);
      return;
    }

    const singleSubBoardWidth = gridRowCount > 1 ? markerWidth / (gridRowCount - 1) : markerWidth;
    const singleSubBoardHeight = gridColumnCount > 1 ? markerHeight / (gridColumnCount - 1) : markerHeight;

    const tlblxInterval = gridColumnCount > 1 ? (curMarkers.bl.x - curMarkers.tl.x) / (gridColumnCount - 1) : 0;
    const tltryInterval = gridRowCount > 1 ? (curMarkers.tr.y - curMarkers.tl.y) / (gridRowCount - 1) : 0;

    const selectionRoiDimension = {
      width: _.get(arrayRegisteration, 'selection_roi.points[1].x', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].x', 0),
      height: _.get(arrayRegisteration, 'selection_roi.points[1].y', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].y', 0),
    };

    const previewTransforms = [_.find(arrayRegisteration.array_transforms, t => t.array_index === 0)];

    for (let i = 1; i <= gridRowCount; i++) {
      for (let j = 1; j <= gridColumnCount; j++) {
        if (i === 1 && j === 1) continue;
        previewTransforms.push({
          array_index: previewTransforms.length,
          flip: {
            x: false,
            y: false,
            center: {
              x: (i - 1) * singleSubBoardWidth + selectionRoiDimension.width / 2 + (j - 1) * tlblxInterval,
              y: (j - 1) * singleSubBoardHeight + selectionRoiDimension.height / 2 + (i - 1) * tltryInterval,
            }
          },
          rotation: {
            angle: 0,
            center: {
              x: (i - 1) * singleSubBoardWidth + selectionRoiDimension.width / 2 + (j - 1) * tlblxInterval,
              y: (j - 1) * singleSubBoardHeight + selectionRoiDimension.height / 2 + (i - 1) * tltryInterval,
            }
          },
          translation: {
            x: (i - 1) * singleSubBoardWidth + (j - 1) * tlblxInterval,
            y: (j - 1) * singleSubBoardHeight + (i - 1) * tltryInterval,
          },
        });
      }
    }

    setPreviewArrayTransforms(previewTransforms);
  }, [curMarkers, gridRowCount, gridColumnCount, arrayRegisteration]);

  const handleGenerateArraySubmit = async (
    arrayRegisteration,
    gridRowCount,
    gridColumnCount,
    curMarkers,
    curProduct,
    step,
    handleRefetchAllComponents,
    refetchArrayRegisteration,
  ) => {
    // if (!curMarkers.tl || !curMarkers.tr || !curMarkers.bl) {
    //   aoiAlert(t('notification.error.pleaseDefineThreeMarkers'), ALERT_TYPES.COMMON_ERROR);
    //   return;
    // }

    if (!curMarkers.tl) {
      aoiAlert(t('notification.error.pleaseDefineTopLeftMarker'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (gridColumnCount > 1 && !curMarkers.bl) {
      aoiAlert(t('notification.error.pleaseDefineBottomLeftMarker'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    if (gridRowCount > 1 && !curMarkers.tr) {
      aoiAlert(t('notification.error.pleaseDefineTopRightMarker'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // check if tl is on the left of tr
    if (gridRowCount > 1 && curMarkers.tl.x >= curMarkers.tr.x) {
      aoiAlert(t('notification.error.invalidMarkerPoint'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    // check if bl is below tl
    if (gridColumnCount > 1 && curMarkers.bl.y <= curMarkers.tl.y) {
      aoiAlert(t('notification.error.invalidMarkerPoint'), ALERT_TYPES.COMMON_ERROR);
      return;
    }

    const markerWidth = gridRowCount > 1 ? curMarkers.tr.x - curMarkers.tl.x : 0;
    const markerHeight = gridColumnCount > 1 ? curMarkers.bl.y - curMarkers.tl.y : 0;

    const singleSubBoardWidth = gridRowCount > 1 ? markerWidth / (gridRowCount - 1) : markerWidth;
    const singleSubBoardHeight = gridColumnCount > 1 ? markerHeight / (gridColumnCount - 1) : markerHeight;

    const tlblxInterval = gridColumnCount > 1 ? (curMarkers.bl.x - curMarkers.tl.x) / (gridColumnCount - 1) : 0;
    const tltryInterval = gridRowCount > 1 ? (curMarkers.tr.y - curMarkers.tl.y) / (gridRowCount - 1) : 0;

    const selectionRoiDimension = {
      width: _.get(arrayRegisteration, 'selection_roi.points[1].x', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].x', 0) + 1,
      height: _.get(arrayRegisteration, 'selection_roi.points[1].y', 0) - _.get(arrayRegisteration, 'selection_roi.points[0].y', 0) + 1,
    };

    const arrayTransforms = [_.find(arrayRegisteration.array_transforms, t => t.array_index === 0)];

    for (let i = 1; i <= gridRowCount; i++) {
      for (let j = 1; j <= gridColumnCount; j++) {
        if (i === 1 && j === 1) continue;
        arrayTransforms.push({
          array_index: arrayTransforms.length,
          flip: {
            x: false,
            y: false,
            center: {
              x: (i - 1) * singleSubBoardWidth + selectionRoiDimension.width / 2 + (j - 1) * tlblxInterval,
              y: (j - 1) * singleSubBoardHeight + selectionRoiDimension.height / 2 + (i - 1) * tltryInterval,
            }
          },
          rotation: {
            angle: 0,
            center: {
              x: (i - 1) * singleSubBoardWidth + selectionRoiDimension.width / 2 + (j - 1) * tlblxInterval,
              y: (j - 1) * singleSubBoardHeight + selectionRoiDimension.height / 2 + (i - 1) * tltryInterval,
            }
          },
          translation: {
            x: (i - 1) * singleSubBoardWidth + (j - 1) * tlblxInterval,
            y: (j - 1) * singleSubBoardHeight + (i - 1) * tltryInterval,
          },
        });
      }
    }

    const payload = {
      product_id: Number(_.get(curProduct, 'product_id', 0)),
      step,
      selection_roi: _.get(arrayRegisteration, 'selection_roi', {}),
      component_ids: _.get(arrayRegisteration, 'component_ids', []),
      array_transforms: arrayTransforms,
    };

    dispatch(setIsContainerLvlLoadingEnabled(true));
    dispatch(setContainerLvlLoadingMsg(t('loader.registeringGoldenArray')));

    const res = await goldenRegisterArray(payload);

    if (res.error) {
      aoiAlert(t('notification.error.registerGoldenArray'), ALERT_TYPES.COMMON_ERROR);
      console.error('goldenRegisterArray error:', _.get(res, 'error.message', ''));
      dispatch(setIsContainerLvlLoadingEnabled(false));
      dispatch(setContainerLvlLoadingMsg(''));
      return;
    }

    await refetchArrayRegisteration();
    await handleRefetchAllComponents(
      Number(_.get(curProduct, 'product_id', 0)),
      step,
    );

    if (setPreviewArrayTransforms) setPreviewArrayTransforms(null);
    if (setCurSelectedMarker) setCurSelectedMarker(null);
    if (setCurMarkers) setCurMarkers({});

    setComponentsActiveTab('templateEditor');

    dispatch(setIsContainerLvlLoadingEnabled(false));
    dispatch(setContainerLvlLoadingMsg(''));
  };

  useEffect(() => {
    if (!curSelectingMarker.current) return;

    setCurMarkers({
      ...curMarkers,
      [curSelectingMarker.current]: curSelectedMarker,
    });

    if (setCurSelectedMarker) setCurSelectedMarker(null);

    curSelectingMarker.current = null;
    setSelectedTool('transform');
  }, [curSelectedMarker]);

  return (
    <div className='flex self-stretch flex-col'>
      <div className='flex w-[348px] p-4 flex-col gap-6'>
        <div className='flex flex-col gap-3 self-stretch'>
          <div className='flex items-center gap-2 self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('productDefine.gridType')}:
            </span>
            <div className='flex items-center justify-center w-6 h-6 rounded-[2px border-[1px] border-gray-2'>
              <img
                src='/icn/grid_white.svg'
                alt='grid'
                className='w-3 h-3'
              />
            </div>
          </div>
          <div className='flex items-center gap-6 self-stretch'>
            <div className='flex gap-2 flex-1 self-stretch items-center'>
              <img
                src='/icn/addRow_white.svg'
                alt='addRow'
                className='w-[14px] h-[14px]'
              />
              <InputNumber
                min={gridColumnCount > 1 ? 1 : 2}
                style={{ width: '120px' }}
                value={gridRowCount}
                onChange={(value) => setGridRowCount(value)}
                precision={0}
              />
            </div>
            <div className='flex items-center gap-2 flex-1 self-stretch'>
              <img
                src='/icn/addCol_white.svg'
                alt='addColumn'
                className='w-[14px] h-[14px]'
              />
              <InputNumber
                min={gridRowCount > 1 ? 1 : 2}
                style={{ width: '120px' }}
                value={gridColumnCount}
                onChange={(value) => setGridColumnCount(value)}
                precision={0}
              />
            </div>
          </div>
        </div>
        <div className='flex flex-col gap-4 self-stretch'>
          <div className='flex flex-col gap-1 self-stretch'>
            <span className='font-source text-[14px] font-normal leading-[150%]'>
              {t('productDefine.defineMarkerPointsOfThree')}
            </span>
            <span className='font-source text-[12px] font-normal leading-[150%]'>
              {t('productDefine.theMarkerPointsOfTheUnit')}
            </span>
          </div>
          <div className='flex items-center gap-2 self-stretch'>
            {_.isEmpty(curMarkers.tl) ?
              <Button
                style={{ width: '284px' }}
                onClick={() => {
                  if (setCurSelectedMarker) setCurSelectedMarker(null);
                  if (setCurMarkers) setCurMarkers(prev => ({ ...prev, tl: undefined }));
                  setSelectedTool('selectMarker');
                  curSelectingMarker.current = 'tl';
                }}
              >
                <div className='flex items-center gap-2 self-stretch'>
                  <img
                    src='/icn/plus_white.svg'
                    alt='plus'
                    className='w-[10px] h-[10px]'
                  />
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                    {t('productDefine.pointATopLeftUnit')}
                  </span>
                </div>
              </Button>
            :
              <Fragment>
                <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                  {`(${_.get(curMarkers, 'tl.x', 0)}, ${_.get(curMarkers, 'tl.y', 0)})`}
                </span>
                <div className='flex items-center flex-1 self-stretch'>
                  <Button
                    style={{ width: '100%' }}
                    onClick={() => {
                      if (setCurSelectedMarker) setCurSelectedMarker(null);
                      if (setCurMarkers) setCurMarkers(prev => ({ ...prev, tl: undefined }));
                      setSelectedTool('selectMarker');
                      curSelectingMarker.current = 'tl';
                    }}
                  >
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('common.edit')}
                    </span>
                  </Button>
                </div>
              </Fragment>
            }
            <Tooltip
              title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointA')}</span>}
              placement='top'
            >
              <div className='flex h-6 w-6 items-center justify-center'>
                <img
                  src='/icn/info_white.svg'
                  alt='info'
                  className='w-3 h-3'
                />
              </div>
            </Tooltip>
          </div>
          {gridRowCount > 1 &&
            <div className='flex items-center gap-2 self-stretch'>
              {_.isEmpty(curMarkers.tr) ?
                <Button
                  style={{ width: '284px' }}
                  onClick={() => {
                    if (setCurSelectedMarker) setCurSelectedMarker(null);
                    if (setCurMarkers) setCurMarkers(prev => ({ ...prev, tr: undefined }));
                    setSelectedTool('selectMarker');
                    curSelectingMarker.current = 'tr';
                  }}
                >
                  <div className='flex items-center gap-2 self-stretch'>
                    <img
                      src='/icn/plus_white.svg'
                      alt='plus'
                      className='w-[10px] h-[10px]'
                    />
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('productDefine.pointBTopRightUnit')}
                    </span>
                  </div>
                </Button>
              :
                <Fragment>
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                    {`(${_.get(curMarkers, 'tr.x', 0)}, ${_.get(curMarkers, 'tr.y', 0)})`}
                  </span>
                  <div className='flex items-center flex-1 self-stretch'>
                    <Button
                      style={{ width: '100%' }}
                      onClick={() => {
                        if (setCurSelectedMarker) setCurSelectedMarker(null);
                        if (setCurMarkers) setCurMarkers(prev => ({ ...prev, tr: undefined }));
                        setSelectedTool('selectMarker');
                        curSelectingMarker.current = 'tr';
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('common.edit')}
                      </span>
                    </Button>
                  </div>
                </Fragment>
              }
              <div className='flex h-6 w-6 items-center justify-center'>
              <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointB')}</span>}
                placement='top'
              >
                <div className='flex h-6 w-6 items-center justify-center'>
                  <img
                    src='/icn/info_white.svg'
                    alt='info'
                    className='w-3 h-3'
                  />
                </div>
              </Tooltip>
              </div>
            </div>
          }
          {gridColumnCount > 1 &&
            <div className='flex items-center gap-2 self-stretch'>
              {_.isEmpty(curMarkers.bl) ?
                <Button
                  style={{ width: '284px' }}
                  onClick={() => {
                    if (setCurSelectedMarker) setCurSelectedMarker(null);
                    if (setCurMarkers) setCurMarkers(prev => ({ ...prev, bl: undefined }));
                    setSelectedTool('selectMarker');
                    curSelectingMarker.current = 'bl';
                  }}
                >
                  <div className='flex items-center gap-2 self-stretch'>
                    <img
                      src='/icn/plus_white.svg'
                      alt='plus'
                      className='w-[10px] h-[10px]'
                    />
                    <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                      {t('productDefine.pointCBottomLeftUnit')}
                    </span>
                  </div>
                </Button>
              :
                <Fragment>
                  <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5 flex-1'>
                    {`(${_.get(curMarkers, 'bl.x', 0)}, ${_.get(curMarkers, 'bl.y', 0)})`}
                  </span>
                  <div className='flex items-center flex-1 self-stretch'>
                    <Button
                      style={{ width: '100%' }}
                      onClick={() => {
                        if (setCurSelectedMarker) setCurSelectedMarker(null);
                        if (setCurMarkers) setCurMarkers(prev => ({ ...prev, bl: undefined }));
                        setSelectedTool('selectMarker');
                        curSelectingMarker.current = 'bl';
                      }}
                    >
                      <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
                        {t('common.edit')}
                      </span>
                    </Button>
                  </div>
                </Fragment>
              }
              <div className='flex h-6 w-6 items-center justify-center'>
                <Tooltip
                title={<span className='font-source text-[12px] font-semibold leading-[150%]'>{t('productDefine.markerPointInfoPointC')}</span>}
                placement='top'
              >
                <div className='flex h-6 w-6 items-center justify-center'>
                  <img
                    src='/icn/info_white.svg'
                    alt='info'
                    className='w-3 h-3'
                  />
                </div>
              </Tooltip>
              </div>
            </div>
          }
        </div>
        <div className='flex items-center gap-2 self-stretch'>
          <Checkbox
            checked={isSubBoardSelectionRoiDisplayed}
            onChange={(e) => {
              setIsSubBoardSelectionRoiDisplayed(e.target.checked);
            }}
          />
          <span className='font-source text-[12px] font-normal leading-[150%] pt-0.5'>
            {t('productDefine.previewArray')}
          </span>
        </div>
      </div>
      <div className='flex items-center p-4 gap-2 self-stretch'>
        <Button
          style={{ width: '50%' }}
          onClick={() => {
            // also clear all markers
            if (setCurMarkers) setCurMarkers({});
            // also clear preview array transforms except the first one
            setPreviewArrayTransforms([_.get(arrayRegisteration, 'array_transforms[0]', {})]);
            setActiveTab('arrayGroup');
          }}
        >
          <span className='font-source text-[12px] font-semibold leading-[normal]'>
            {t('common.cancel')}
          </span>
        </Button>
        <Button
          type='primary'
          style={{ width: '50%' }}
          onClick={() => {
            handleGenerateArraySubmit(
              arrayRegisteration,
              gridRowCount,
              gridColumnCount,
              curMarkers,
              curProduct,
              0,
              handleRefetchAllComponents,
              refetchArrayRegisteration,
            );
          }}
        >
          <span className='font-source text-[12px] font-semibold leading-[normal]'>
            {t('productDefine.createArray')}
          </span>
        </Button>
      </div>
    </div>
  );
};

export default SmartPCBArray;