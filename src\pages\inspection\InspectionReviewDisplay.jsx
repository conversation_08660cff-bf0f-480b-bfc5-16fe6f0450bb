import React, { Fragment, useState } from 'react';
import InspectionReviewFeatureViewer from '../../viewer/InspectionReviewFeatureViewer';
import _ from 'lodash';
import InspectionReviewFullViewer from '../../viewer/InspectionReviewFullViewer';
import InspectionReviewFeatureDetailRow from './InspectionReviewFeatureDetailRow';


const InspectionReviewDisplay = (props) => {
  const {
    goldenFeatures,
    goldenComponents,
    inspectedFeatures,
    inspectedComponents,
    selectedRCid,
    selectedDCid,
    selectedFid,
    goldenProduct,
    selectedArrayIndex,
    maskImage,
  } = props;

  const [displayDimensionMode, setDisplayDimensionMode] = useState('2d'); // 2d, 3d(pc)
  const [isDepthMapDisplayed, setIsDepthMapDisplayed] = useState(false);
  const [pointCloudDisplayedView, setPointCloudDisplayedView] = useState('top'); // top, front, back, left, right
  const [activeHeightDiffComparisonView, setActiveHeightDiffComparisonView] = useState('comparison'); // comparison, overlaid

  const [isIpcCloudVisible, setIsIpcCloudVisible] = useState(true);
  const [isGoldenCloudVisible, setIsGoldenCloudVisible] = useState(true);

  // useEffect(() => {
  //   if (displayDimensionMode === '2d') {
  //     setIsDepthMapDisplayed(false);
  //   } else if (displayDimensionMode === '3d') {
  //     setPointCloudDisplayedView('top');
  //     setActiveHeightDiffComparisonView('comparison');
  //   }
  // }, [displayDimensionMode]);

  return (
    <Fragment>
      <div className='flex items-center flex-1 gap-0.5 self-stretch'>
        {/* <div className='flex flex-1 self-stretch rounded-[4px] border-[4px] border-[#F46D6D] bg-[#000]'>
          <InspectionReviewFeatureViewer
            componentInfo={_.find(inspectedComponents, c => c.result_component_id === selectedRCid)}
            featureInfo={_.find(inspectedFeatures, f => f.component_id === selectedRCid && f.feature_id === selectedFid)}
            isErroredSample={!_.get(_.find(inspectedFeatures, f => f.component_id === selectedRCid && f.feature_id === selectedFid), 'pass', false)}
            isInspectedView={true}
            maskImage={maskImage}
          />
        </div>
        <div className='flex flex-1 self-stretch rounded-[4px] border-[4px] border-[#81F499] bg-[#000]'>
          <InspectionReviewFeatureViewer
            componentInfo={_.find(goldenComponents, c => c.region_group_id === selectedDCid && c.array_index === selectedArrayIndex)}
            featureInfo={_.find(goldenFeatures, c => c.group_id === selectedDCid && c.feature_id === selectedFid && c.array_index === selectedArrayIndex)}
            isErroredSample={false}
            isInspectedView={false}
          />
        </div> */}
        <InspectionReviewFeatureDetailRow
          displayDimensionMode={displayDimensionMode}
          setDisplayDimensionMode={setDisplayDimensionMode}
          inspectedComponents={inspectedComponents}
          inspectedFeatures={inspectedFeatures}
          selectedDCid={selectedDCid}
          selectedRCid={selectedRCid}
          selectedFid={selectedFid}
          selectedArrayIndex={selectedArrayIndex}
          maskImage={maskImage}
          goldenComponents={goldenComponents}
          goldenFeatures={goldenFeatures}
          isDepthMapDisplayed={isDepthMapDisplayed}
          setIsDepthMapDisplayed={setIsDepthMapDisplayed}
          pointCloudDisplayedView={pointCloudDisplayedView}
          setPointCloudDisplayedView={setPointCloudDisplayedView}
          activeHeightDiffComparisonView={activeHeightDiffComparisonView}
          setActiveHeightDiffComparisonView={setActiveHeightDiffComparisonView}
          goldenProduct={goldenProduct}
          isIpcCloudVisible={isIpcCloudVisible}
          isGoldenCloudVisible={isGoldenCloudVisible}
          setIsIpcCloudVisible={setIsIpcCloudVisible}
          setIsGoldenCloudVisible={setIsGoldenCloudVisible}
        />
      </div>
      <div className='flex items-center flex-1 gap-0.5 self-stretch'>
        <div className='flex flex-1 self-stretch rounded-[4px]'>
          <InspectionReviewFullViewer
            // components={_.filter(inspectedComponents, c => c.result_component_id === selectedRCid)}
            // features={_.filter(inspectedFeatures, f => f.component_id === selectedRCid)}
            components={inspectedComponents}
            features={inspectedFeatures}
            isInspectedView={true}
            selectedDCid={selectedRCid}
            selectedFid={selectedFid}
            selectedArrayIndex={selectedArrayIndex}
          />
        </div>
        <div className='flex flex-1 self-stretch rounded-[4px]'>
          <InspectionReviewFullViewer
            components={goldenComponents}
            features={goldenFeatures}
            isInspectedView={false}
            goldenProduct={goldenProduct}
            selectedDCid={selectedDCid}
            selectedFid={selectedFid}
            selectedArrayIndex={selectedArrayIndex}
          />
        </div>
      </div>
    </Fragment>
  );
};

export default InspectionReviewDisplay;