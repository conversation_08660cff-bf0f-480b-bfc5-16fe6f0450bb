import { Button, Input, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { localStorageKeys } from '../../../common/const';

function Host() {
	const { t } = useTranslation();

	const [uriPrefix, setUriPrefix] = useState('http://');
	const [newHost, setNewHost] = useState('');

	useEffect(() => {
		const serverEndpoint =
			localStorage.getItem('serverHost') || 'http://localhost:8005';
		setUriPrefix(serverEndpoint.split('://')[0] + '://');
		setNewHost(serverEndpoint.split('://')[1]);
	}, []);

	return (
		<div className="flex py-4 px-[140px] flex-1 self-stretch justify-center">
			<div className="flex w-[800px] flex-col gap-0.5 self-stretch">
				<div className="flex p-6 items-center self-stretch">
					<span className="font-source text-[20px] font-normal leading-[normal] tracking-[0.6px]">
						{t('updateBackendHost.title')}
					</span>
				</div>
				{/* add system language desc */}
				<div className="flex flex-col  items-start gap-2 px-4">
					<span className="font-source text-[14px] text-gray-3 font-normal leading-[normal] tracking-[0.6px]">
						{t('updateBackendHost.titleDesc1')}
					</span>
					<span className="font-source text-[14px] text-gray-3 font-normal leading-[normal] tracking-[0.6px]">
						{t('updateBackendHost.titleDesc2')}
					</span>
				</div>

				<div className="flex flex-col items-start gap-8 self-stretch px-4 py-6">
					<div className="flex items-center gap-2 self-stretch">
						<span className="font-source text-[14px] font-normal whitespace-nowrap">
							{t('updateBackendHost.currentHost')}
						</span>
						<div className="flex gap-1 items-center w-full">
							<Select
								size="small"
								options={[
									{
										label: 'http://',
										value: 'http://',
									},
									// {
									// 	label: 'https://',
									// 	value: 'https://',
									// },
								]}
								value={uriPrefix}
								popupMatchSelectWidth={false}
								onChange={(value) => setUriPrefix(value)}
								style={{ width: '120px' }}
							/>
							<Input
								size="small"
								value={newHost}
								onChange={(e) => setNewHost(e.target.value)}
								style={{ width: '100%' }}
							/>
						</div>
					</div>
				</div>
				<div className='flex gap-2 items-center justify-start px-4 py-6'>
					<Button
						onClick={() => {
							localStorage.setItem('serverHost', `${uriPrefix}${newHost}`);
							localStorage.setItem(localStorageKeys.accessToken, '');
							localStorage.setItem(localStorageKeys.userRole, '');
							window.location.href = '/login';
						}}
					>
						<span
							className='font-source text-[12px] font-normal'
						>
							{t('common.save')}
						</span>
					</Button>
				</div>
			</div>
		</div>
	);
}

export default Host;
